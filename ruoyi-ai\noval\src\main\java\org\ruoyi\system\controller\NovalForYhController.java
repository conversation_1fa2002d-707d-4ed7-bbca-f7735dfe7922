package org.ruoyi.system.controller;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.ruoyi.common.core.domain.R;
import org.ruoyi.common.log.annotation.Log;
import org.ruoyi.common.log.enums.BusinessType;
import org.ruoyi.system.service.INovalForYhServiceByES;
import org.ruoyi.system.util.Es.ElasticsearchIndexUtil;
import org.ruoyi.system.util.Es.EsIndex;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.ruoyi.common.web.core.BaseController;
import org.ruoyi.core.page.PageQuery;
import org.ruoyi.system.domain.vo.NovalForYhVo;
import org.ruoyi.system.domain.bo.NovalForYhBo;
import org.ruoyi.core.page.TableDataInfo;

/**
 * 用户浏览书库
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/noval/NovalForYh")
public class NovalForYhController extends BaseController {

    private final INovalForYhServiceByES novalForYhService;
    /**
     * 查询用户浏览书库列表
     */
    @GetMapping("/list")
    public TableDataInfo<NovalForYhVo> list(NovalForYhBo bo, PageQuery pageQuery) {
        TableDataInfo<NovalForYhVo> novalForYhVoTableDataInfo = novalForYhService.queryPageList(bo, pageQuery);
        return novalForYhVoTableDataInfo;
    }

    /**
     * 获取用户浏览书库详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<NovalForYhVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(novalForYhService.queryById(id));
    }

    @Log(title = "我的小说")
    @GetMapping("/getInfo/{ids}")
    public R<List<NovalForYhVo>> getList(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        List<NovalForYhVo> novalForYhVos = novalForYhService.queryByUserId(ids);
        return R.ok(novalForYhVos);
    }

    @GetMapping("/api/suggest")
    public List<String> suggestProducts(@RequestBody Map<String, String> params) throws IOException {
        try (ElasticsearchClient client = ElasticsearchIndexUtil.createClient()) {
            //用户输入的书名
            String name_suggest = String.valueOf(params.get("name_suggest"));
            //用户当前输入的内容
            String keyword = String.valueOf(params.get("keyword"));
            return ElasticsearchIndexUtil.autoSuggest(
                    client,
                    EsIndex.NovalForYh,keyword,name_suggest
            );
        }
    }

    @GetMapping("/suggest")
    public List<String> suggestNovalNames(@RequestParam String keyword) {
        return novalForYhService.autoSuggest(keyword);
    }
}
