package io.github.linpeilie;

import org.ruoyi.system.domain.NovalForYh;
import org.ruoyi.system.domain.NovalForYhToNovalForYhVoMapper;
import org.ruoyi.system.domain.bo.NovalForYhBo;
import org.ruoyi.system.domain.bo.NovalForYhBoToNovalForYhMapper;
import org.ruoyi.system.domain.vo.NovalForYhVo;
import org.ruoyi.system.domain.vo.NovalForYhVoToNovalForYhMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
public class ConvertMapperAdapter {
  private NovalForYhVoToNovalForYhMapper novalForYhVoToNovalForYhMapper;

  private NovalForYhToNovalForYhVoMapper novalForYhToNovalForYhVoMapper;

  private NovalForYhBoToNovalForYhMapper novalForYhBoToNovalForYhMapper;

  @Autowired
  public void setNovalForYhVoToNovalForYhMapper(@Lazy NovalForYhVoToNovalForYhMapper novalForYhVoToNovalForYhMapper) {
    this.novalForYhVoToNovalForYhMapper = novalForYhVoToNovalForYhMapper;
  }

  @Autowired
  public void setNovalForYhToNovalForYhVoMapper(@Lazy NovalForYhToNovalForYhVoMapper novalForYhToNovalForYhVoMapper) {
    this.novalForYhToNovalForYhVoMapper = novalForYhToNovalForYhVoMapper;
  }

  @Autowired
  public void setNovalForYhBoToNovalForYhMapper(@Lazy NovalForYhBoToNovalForYhMapper novalForYhBoToNovalForYhMapper) {
    this.novalForYhBoToNovalForYhMapper = novalForYhBoToNovalForYhMapper;
  }

  public NovalForYh novalForYhVoToNovalForYh(NovalForYhVo novalForYhVo) {
    return novalForYhVoToNovalForYhMapper.convert(novalForYhVo);
  }

  public NovalForYhVo novalForYhToNovalForYhVo(NovalForYh novalForYh) {
    return novalForYhToNovalForYhVoMapper.convert(novalForYh);
  }

  public NovalForYh novalForYhBoToNovalForYh(NovalForYhBo novalForYhBo) {
    return novalForYhBoToNovalForYhMapper.convert(novalForYhBo);
  }
}
