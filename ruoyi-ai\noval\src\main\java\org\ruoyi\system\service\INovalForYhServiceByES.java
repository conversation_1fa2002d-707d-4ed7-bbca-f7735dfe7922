package org.ruoyi.system.service;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.Result;
import org.ruoyi.core.page.PageQuery;

import org.ruoyi.core.page.TableDataInfo;
import org.ruoyi.system.domain.bo.NovalForYhBo;

import org.ruoyi.system.domain.vo.NovalForYhVo;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface INovalForYhServiceByES {
    /**
     * 查询用户浏览书库列表
     */
    public TableDataInfo<NovalForYhVo> queryPageList(NovalForYhBo bo, PageQuery pageQuery);
    public List<NovalForYhVo> loadData(NovalForYhBo bo, PageQuery pageQuery);

    public NovalForYhVo queryById(Long id) ;
    public NovalForYhVo updateEs(ElasticsearchClient client, Long id) throws IOException;
    public Result updateEsByMap(Map<String,Object> map,Long id) ;

    public void deleteEs(ElasticsearchClient client, Long id);
    public boolean addEs(NovalForYhVo novalForYhVo);

    List<NovalForYhVo> queryByUserId(Long[] ids);
     List<String> autoSuggest(String keyword);
}
