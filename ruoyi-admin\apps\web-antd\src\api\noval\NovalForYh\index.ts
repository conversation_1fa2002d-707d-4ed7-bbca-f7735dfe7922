import type { NovalForYhVO, NovalForYhForm, NovalForYhQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询用户浏览书库列表
* @param params
* @returns 用户浏览书库列表
*/
export function NovalForYhList(params?: NovalForYhQuery) {
  // 如果有ID参数，使用getInfo接口，否则使用list接口
  if (params?.id) {
    return requestClient.get<PageResult<NovalForYhVO>>(`/noval/NovalForYh/getInfo/${params.id}`);
  }
  return requestClient.get<PageResult<NovalForYhVO>>('/noval/NovalForYh/list', { params });
}

/**
 * 导出用户浏览书库列表
 * @param params
 * @returns 用户浏览书库列表
 */
export function NovalForYhExport(params?: NovalForYhQuery) {
  return commonExport('/noval/NovalForYh/export', params ?? {});
}

/**
 * 查询用户浏览书库详情
 * @param id id
 * @returns 用户浏览书库详情
 */
export function NovalForYhInfo(id: ID) {
  return requestClient.get<NovalForYhVO>(`/noval/NovalForYh/${id}`);
}

/**
 * 新增用户浏览书库
 * @param data
 * @returns void
 */
export function NovalForYhAdd(data: NovalForYhForm) {
  return requestClient.postWithMsg<void>('/noval/NovalForYh', data);
}

/**
 * 更新用户浏览书库
 * @param data
 * @returns void
 */
export function NovalForYhUpdate(data: NovalForYhForm) {
  return requestClient.putWithMsg<void>('/noval/NovalForYh', data);
}

/**
 * 删除用户浏览书库
 * @param id id
 * @returns void
 */
export function NovalForYhRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/NovalForYh/${id}`);
}

export function NovalForYhGetInfoByIds(ids: number[]) {
  if (ids.length === 0) {
    return Promise.resolve([]);
  }
  return requestClient.get<NovalForYhVO[]>(`/noval/NovalForYh/getInfo/${ids.join(',')}`);
}

/**
 * 搜索框自动补全
 * @param keyword 关键词
 * @returns 自动补全建议列表
 */
export function NovalForYhSuggest(keyword: string) {
  return requestClient.get<string[]>('/noval/NovalForYh/suggest', {
    params: { keyword }
  });
}

/**
 * 用户登录完成后执行，f5后也要执行
 * @returns void
 */
export function NovalForYhInit() {
  return requestClient.post<void>('/noval/NovalForYh/init');
}
