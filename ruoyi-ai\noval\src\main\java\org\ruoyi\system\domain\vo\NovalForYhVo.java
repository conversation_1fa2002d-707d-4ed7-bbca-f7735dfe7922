package org.ruoyi.system.domain.vo;

import lombok.NoArgsConstructor;
import org.ruoyi.common.excel.annotation.ExcelDictFormat;
import org.ruoyi.common.excel.convert.ExcelDictConvert;
import org.ruoyi.system.domain.Noval;
import org.ruoyi.system.domain.NovalForYh;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 用户浏览书库视图对象 noval_for_yh
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
@ExcelIgnoreUnannotated
@NoArgsConstructor
@AutoMapper(target = NovalForYh.class)
public class NovalForYhVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 小说id
     */
    @ExcelProperty(value = "小说id")
    private Long id;

    /**
     * 小说名称
     */
    @ExcelProperty(value = "小说名称")
    private String name;

    /**
     * 小说作者
     */
    @ExcelProperty(value = "小说作者")
    private String author;

    /**
     * 小说状态
     */
    @ExcelProperty(value = "小说状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_noval_state")
    private String flag;

    /**
     * 小说平台
     */
    @ExcelProperty(value = "小说平台")
    private String platform;

    /**
     * 小说成绩
     */
    @ExcelProperty(value = "小说成绩", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_noval_platform")
    private Long grades;

    /**
     * 小说类型
     */
    @ExcelProperty(value = "小说类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_noval_type")
    private String type;

    /**
     * 小说简介
     */
    @ExcelProperty(value = "小说简介")
    private String summary;

    /**
     * 小说图标
     */
    @ExcelProperty(value = "小说图标")
    private String icon;

    /**
     * 点击次数
     */
    @ExcelProperty(value = "点击次数")
    private String look;

    /**
     *  自动补全字段
     */
    @ExcelProperty(value = "自动补全字段")
    private String name_suggest;

    public NovalForYhVo(NovalVo novalVo, NovalDetailsVo novalDetailsVo) {
        this.id = novalDetailsVo.getId();
        this.name = novalVo.getName();
        this.author = novalVo.getAuthor();
        this.flag = novalVo.getFlag();
        this.grades = novalDetailsVo.getGrades();
        this.platform = novalDetailsVo.getPlatform();
        this.type = novalDetailsVo.getType();
        this.icon = novalDetailsVo.getIcon();
        this.summary= novalDetailsVo.getSummary();
        this.look = novalDetailsVo.getLook();
        this.name_suggest = novalVo.getName();
    }

    public NovalForYhVo(Noval noval) {
        this.id = noval.getId();
        this.name = noval.getName();
        this.author = noval.getAuthor();
        this.flag = noval.getFlag();
        this.grades = 0L;
        this.platform = "";
        this.type = "";
        this.icon = "";
        this.summary= "";
        this.look = "0";
        this.name_suggest = noval.getName();
    }
}
