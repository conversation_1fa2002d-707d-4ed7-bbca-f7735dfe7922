{"doc": " 用户浏览书库Service业务层处理\n\n <AUTHOR>\n @date 2025-06-11\n", "fields": [], "enumConstants": [], "methods": [{"name": "creteIndex", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient"], "doc": " 创建索引\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.NovalForYhBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询用户浏览书库列表\n"}, {"name": "loadData", "paramTypes": ["org.ruoyi.system.domain.bo.NovalForYhBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询用户浏览书库列表\n"}], "constructors": []}