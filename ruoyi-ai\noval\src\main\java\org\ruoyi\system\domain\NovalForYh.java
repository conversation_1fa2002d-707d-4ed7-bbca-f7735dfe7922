package org.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.ruoyi.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 用户浏览书库对象 noval_for_yh
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("noval_for_yh")
public class NovalForYh extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 小说id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 小说名称
     */
    private String name;

    /**
     * 小说作者
     */
    private String author;

    /**
     * 小说状态
     */
    private String flag;

    /**
     * 小说平台
     */
    private String platform;

    /**
     * 小说成绩
     */
    private Long grades;

    /**
     * 小说类型
     */
    private String type;

    /**
     * 小说简介
     */
    private String summary;

    /**
     * 小说图标
     */
    private String icon;
    /**
     * 点击次数
     */
    private String look;

    /**
     *  自动补全字段
     */
    private String name_suggest;
}
