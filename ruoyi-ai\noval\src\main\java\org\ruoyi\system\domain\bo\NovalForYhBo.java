package org.ruoyi.system.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.NoArgsConstructor;
import org.ruoyi.common.core.validate.AddGroup;
import org.ruoyi.common.core.validate.EditGroup;
import org.ruoyi.system.domain.NovalForYh;
import org.ruoyi.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 用户浏览书库业务对象 noval_for_yh
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = NovalForYh.class, reverseConvertGenerate = false)
@NoArgsConstructor
public class NovalForYhBo extends BaseEntity {
    /**
     * 书本id
     */
    @NotNull(message = "书本id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long id;

    /**
     * 小说名称
     */
    @NotBlank(message = "小说名称不能为空", groups = {AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 小说作者
     */
    @NotBlank(message = "小说作者不能为空", groups = { AddGroup.class, EditGroup.class })
    private String author;

    /**
     * 小说平台
     */
    @NotBlank(message = "小说平台不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platform;

    /**
     * 小说成绩
     */
    @NotNull(message = "小说成绩不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long grades;

    /**
     * 小说类型
     */
    @NotBlank(message = "小说类型不能为空", groups = { AddGroup.class, EditGroup.class  })
    private String type;

    /**
     * 点击次数
     */
    private String look;

    /**
     *  自动补全字段
     */
    private String name_suggest;

}
