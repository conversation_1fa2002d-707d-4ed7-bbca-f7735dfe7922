package org.ruoyi.system.domain.vo;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.NovalForYh;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T10:22:39+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalForYhVoToNovalForYhMapperImpl implements NovalForYhVoToNovalForYhMapper {

    @Override
    public NovalForYh convert(NovalForYhVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalForYh novalForYh = new NovalForYh();

        novalForYh.setId( arg0.getId() );
        novalForYh.setName( arg0.getName() );
        novalForYh.setAuthor( arg0.getAuthor() );
        novalForYh.setFlag( arg0.getFlag() );
        novalForYh.setPlatform( arg0.getPlatform() );
        novalForYh.setGrades( arg0.getGrades() );
        novalForYh.setType( arg0.getType() );
        novalForYh.setSummary( arg0.getSummary() );
        novalForYh.setIcon( arg0.getIcon() );
        novalForYh.setLook( arg0.getLook() );
        novalForYh.setName_suggest( arg0.getName_suggest() );

        return novalForYh;
    }

    @Override
    public NovalForYh convert(NovalForYhVo arg0, NovalForYh arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setAuthor( arg0.getAuthor() );
        arg1.setFlag( arg0.getFlag() );
        arg1.setPlatform( arg0.getPlatform() );
        arg1.setGrades( arg0.getGrades() );
        arg1.setType( arg0.getType() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setLook( arg0.getLook() );
        arg1.setName_suggest( arg0.getName_suggest() );

        return arg1;
    }
}
