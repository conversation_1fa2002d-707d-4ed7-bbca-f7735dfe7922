package org.ruoyi.system.domain;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.NovalForYhVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T10:22:39+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalForYhToNovalForYhVoMapperImpl implements NovalForYhToNovalForYhVoMapper {

    @Override
    public NovalForYhVo convert(NovalForYh arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalForYhVo novalForYhVo = new NovalForYhVo();

        novalForYhVo.setId( arg0.getId() );
        novalForYhVo.setName( arg0.getName() );
        novalForYhVo.setAuthor( arg0.getAuthor() );
        novalForYhVo.setFlag( arg0.getFlag() );
        novalForYhVo.setPlatform( arg0.getPlatform() );
        novalForYhVo.setGrades( arg0.getGrades() );
        novalForYhVo.setType( arg0.getType() );
        novalForYhVo.setSummary( arg0.getSummary() );
        novalForYhVo.setIcon( arg0.getIcon() );
        novalForYhVo.setLook( arg0.getLook() );
        novalForYhVo.setName_suggest( arg0.getName_suggest() );

        return novalForYhVo;
    }

    @Override
    public NovalForYhVo convert(NovalForYh arg0, NovalForYhVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setAuthor( arg0.getAuthor() );
        arg1.setFlag( arg0.getFlag() );
        arg1.setPlatform( arg0.getPlatform() );
        arg1.setGrades( arg0.getGrades() );
        arg1.setType( arg0.getType() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setLook( arg0.getLook() );
        arg1.setName_suggest( arg0.getName_suggest() );

        return arg1;
    }
}
