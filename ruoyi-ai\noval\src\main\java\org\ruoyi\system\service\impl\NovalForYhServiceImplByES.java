package org.ruoyi.system.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.Result;
import co.elastic.clients.elasticsearch._types.mapping.LongNumberProperty;
import co.elastic.clients.elasticsearch._types.mapping.Property;
import co.elastic.clients.elasticsearch._types.mapping.TextProperty;
import lombok.RequiredArgsConstructor;
import org.ruoyi.core.page.PageQuery;
import org.ruoyi.core.page.TableDataInfo;
import org.ruoyi.system.domain.bo.NovalDetailsBo;
import org.ruoyi.system.domain.bo.NovalForYhBo;
import org.ruoyi.system.domain.bo.SysDictDataBo;
import org.ruoyi.system.domain.vo.NovalDetailsVo;
import org.ruoyi.system.domain.vo.NovalForYhVo;
import org.ruoyi.system.domain.vo.NovalVo;
import org.ruoyi.system.domain.vo.SysDictDataVo;
import org.ruoyi.system.service.INovalDetailsService;
import org.ruoyi.system.service.INovalForYhServiceByES;
import org.ruoyi.system.service.INovalService;
import org.ruoyi.system.util.*;
import org.ruoyi.system.util.Cuckoo.CuckooFilterDict;
import org.ruoyi.system.util.Cuckoo.CuckooFilterUtil;
import org.ruoyi.system.util.Es.ElasticsearchIndexUtil;
import org.ruoyi.system.util.Es.EsFenCi;
import org.ruoyi.system.util.Es.EsIndex;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户浏览书库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RequiredArgsConstructor
@Service
public class NovalForYhServiceImplByES implements INovalForYhServiceByES {

    private final INovalService novalService;
    private final INovalDetailsService novalDetailsService;
    private final CuckooFilterUtil<String> cuckooFilterUtilForId;
    private final CuckooFilterUtil<String> cuckooFilterUtilForPlatform;
    private final CuckooFilterUtil<String> cuckooFilterUtilForType;
    private final SysDictDataServiceImpl dictData;
    /**
     * 创建索引
     */
    private void creteIndex(ElasticsearchClient client) throws IOException {
        // 创建索引
        Map<String, Property> properties = new HashMap<>();
        properties.put("id", Property.of(p -> p.long_(LongNumberProperty.of(l -> l))));
        properties.put("name", Property.of(p -> p.text(TextProperty.of(t -> t.analyzer(EsFenCi.ik_max_word)))));
        properties.put("author", Property.of(p -> p.text(TextProperty.of(t -> t.fielddata(true)))));
        properties.put("flag", Property.of(p -> p.keyword(t -> t)));
        properties.put("platform", Property.of(p -> p.text(t -> t)));
        properties.put("grades", Property.of(p -> p.text(t -> t)));
        properties.put("type",Property.of(p -> p.text(t -> t)));
        properties.put("summary",Property.of(p -> p.text(t -> t)));
        properties.put("icon",Property.of(p -> p.text(t -> t)));
        properties.put("look",Property.of(p -> p.text(t -> t)));

        // 添加自动补全字段
        properties.put("name_suggest", Property.of(p -> p
                .completion(c -> c
                        .analyzer(EsFenCi.ik_max_word)
                        .preserveSeparators(false)
                        .preservePositionIncrements(true)
                )
        ));
        boolean result = client.indices().exists(b -> b.index(EsIndex.NovalForYh)).value();
        if (result) {
            // 删除现有索引
            client.indices().delete(b -> b.index(EsIndex.NovalForYh));
        }
        result = ElasticsearchIndexUtil.createIndex(client, EsIndex.NovalForYh, properties);
        if (result){
            System.out.println("创建索引成功:"+EsIndex.NovalForYh);
        }else {
            System.out.println("创建索引失败:"+EsIndex.NovalForYh);
        }

    }

    private void createCuckooFilter() {
        SysDictDataBo sysDictDataBo = new SysDictDataBo();
        sysDictDataBo.setDictType(CuckooFilterDict.NovalType);
        List<SysDictDataVo> sysDictDataVos = dictData.selectDictDataList(sysDictDataBo);
        sysDictDataVos.forEach(sysDictDataVo -> {
            cuckooFilterUtilForType.add(sysDictDataVo.getDictValue());
        });
        sysDictDataBo.setDictType(CuckooFilterDict.NovalPlatform);
        sysDictDataVos = dictData.selectDictDataList(sysDictDataBo);
        sysDictDataVos.forEach(sysDictDataVo -> {
            cuckooFilterUtilForPlatform.add(sysDictDataVo.getDictValue());
        });
    }

    public void init() {
        ElasticsearchClient client = ElasticsearchIndexUtil.createClient();
        createCuckooFilter();
        try {
            creteIndex(client);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    /**
     * 查询用户浏览书库列表
     */
    public TableDataInfo<NovalForYhVo> queryPageList(NovalForYhBo bo, PageQuery pageQuery) {
        ElasticsearchClient client = ElasticsearchIndexUtil.createClient();
        TableDataInfo<NovalForYhVo> novalForYhVoTableDataInfo;
        List<NovalForYhVo> novalForYhVoList;
        try {
            boolean b = ElasticsearchIndexUtil.indexExists(client, EsIndex.NovalForYh);
            if(!b){
                novalForYhVoList = loadData(bo, pageQuery);
                Map<String,NovalForYhVo> map = new HashMap<>();
                novalForYhVoList.forEach(vo -> map.put(String.valueOf(vo.getId()),vo));
                ElasticsearchIndexUtil.bulkAddDocuments(client, EsIndex.NovalForYh,map);
            }else {
                novalForYhVoList = ElasticsearchIndexUtil.searchDocuments(client, EsIndex.NovalForYh,
                        "", // 空字段表示匹配所有
                        "", // 空值触发match_all
                        NovalForYhVo.class);
            }
            novalForYhVoTableDataInfo = TableDataInfo.build(novalForYhVoList);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            client.shutdown();
        }
        return novalForYhVoTableDataInfo;
    }


    /**
     * 查询用户浏览书库列表
     */
    public List<NovalForYhVo> loadData(NovalForYhBo bo, PageQuery pageQuery) {
        List<NovalForYhVo> novalForYhVoList = new ArrayList<>();
        String platform = bo.getPlatform();
        String type = bo.getType();
        NovalDetailsBo novalDetailsBo = new NovalDetailsBo();
        if(!(cuckooFilterUtilForPlatform.contains(platform) && cuckooFilterUtilForType.contains(type))){
            return novalForYhVoList;
        }
        novalDetailsBo.setPlatform(platform);
        novalDetailsBo.setType(type);
        TableDataInfo<NovalDetailsVo> novalDetailsVoTableDataInfo = novalDetailsService.queryPageList(novalDetailsBo, pageQuery);
        novalDetailsVoTableDataInfo.getRows().forEach(novalDetailsVo -> {
            NovalVo novalVo = novalService.queryById(novalDetailsVo.getId());
            NovalForYhVo novalForYhVo = new NovalForYhVo(novalVo, novalDetailsVo);
            if(!novalForYhVo.getFlag().equals("0")) {
                cuckooFilterUtilForId.add(String.valueOf(novalForYhVo.getId()));
                novalForYhVoList.add(novalForYhVo);
            }
        });
        SortUtils.sortList(novalForYhVoList, NovalForYhVo::getLook, SortUtils.ASCENDING);
        return novalForYhVoList;
    }


    public NovalForYhVo queryById(Long id) {
        ElasticsearchClient client = ElasticsearchIndexUtil.createClient();
        NovalForYhVo novalForYhVo;
        try {
            novalForYhVo = ElasticsearchIndexUtil.getDocument(client, EsIndex.NovalForYh, String.valueOf(id), NovalForYhVo.class);
            if(novalForYhVo == null){
                novalForYhVo = updateEs(client, id);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            client.shutdown();
        }
        return novalForYhVo;
    }

    public boolean addEs(NovalForYhVo novalForYhVo) {
        ElasticsearchClient client = ElasticsearchIndexUtil.createClient();
        try {
            cuckooFilterUtilForId.add(String.valueOf(novalForYhVo.getId()));
            ElasticsearchIndexUtil.addDocument(client, EsIndex.NovalForYh,novalForYhVo);
        }catch (IOException e){
            System.out.println("addEs方法添加数据到ES失败");
            return false;
        }finally {
            client.shutdown();
        }
        return true;
    }
    public NovalForYhVo updateEs(ElasticsearchClient client, Long id) throws IOException {
        NovalVo novalVo = novalService.queryById(id);
        NovalDetailsVo novalDetailsVo = novalDetailsService.queryById(id);
        if (novalVo == null || novalDetailsVo == null) {
            throw new IllegalArgumentException("未找到ID为 " + id + " 的小说基础数据");
        }
        NovalForYhVo novalForYhVo = new NovalForYhVo(novalVo, novalDetailsVo);
        ElasticsearchIndexUtil.updateDocument(client, EsIndex.NovalForYh, String.valueOf(id),novalForYhVo);
        return novalForYhVo;
    }

    public Result updateEsByMap(Map<String,Object> map,Long id) {
        ElasticsearchClient client = ElasticsearchIndexUtil.createClient();
        cuckooFilterUtilForId.add(String.valueOf(id));
        Result result;
        try {
            result = ElasticsearchIndexUtil.updateDocument(client, EsIndex.NovalForYh, String.valueOf(id),map);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            client.shutdown();
        }
        return result;
    }

    public void deleteEs(ElasticsearchClient client, Long id) {
        try {
            cuckooFilterUtilForId.remove(String.valueOf(id));
            ElasticsearchIndexUtil.deleteDocument(client, EsIndex.NovalForYh, String.valueOf(id));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<NovalForYhVo> queryByUserId(Long[] ids) {
        if(ids == null || ids.length == 0){
            return null;
        }
        List<NovalForYhVo> novalForYhVoList = new ArrayList<>();
            for(Long id : ids){
                boolean contains = cuckooFilterUtilForId.contains(String.valueOf(id));
                if(contains) {
                    novalForYhVoList.add(queryById(id));
                }
            }
        return novalForYhVoList;
    }

    @Override
    public List<String> autoSuggest(String keyword) {
        ElasticsearchClient client = ElasticsearchIndexUtil.createClient();
        try {
            return ElasticsearchIndexUtil.autoSuggest(
                    client,
                    EsIndex.NovalForYh,
                    keyword,
                    "name_suggest"  // 使用我们定义的补全字段
            );
        } catch (IOException e) {
            throw new RuntimeException("自动补全查询失败", e);
        } finally {
            client.shutdown();
        }
    }
}
