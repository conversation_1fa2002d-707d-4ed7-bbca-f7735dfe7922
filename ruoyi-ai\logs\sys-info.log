2025-08-07 09:14:34 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-07 09:14:34 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 21376 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-07 09:14:34 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-07 09:14:50 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-07 09:14:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-07 09:14:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-07 09:14:52 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@13cec4ff
2025-08-07 09:14:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-07 09:14:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-07 09:14:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-07 09:14:54 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-07 09:14:55 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-07 09:14:55 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-07 09:14:55 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-07 09:14:55 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-07 09:14:57 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-07 09:15:02 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-07 09:15:02 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-07 09:15:02 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-07 09:15:02 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-07 09:15:02 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 29.186 seconds (process running for 48.397)
2025-08-07 09:15:02 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-08-07 09:15:02 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-07 09:18:34 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07 09:18:34 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 09:18:35 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 09:18:37 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:08:08 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-07 10:08:08 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-07 10:08:10 [SpringApplicationShutdownHook] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-08-07 10:08:10 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-07 10:08:10 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-07 10:08:10 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-07 10:08:10 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-07 10:08:10 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-07 10:08:27 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-07 10:08:27 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 14192 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-07 10:08:27 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-07 10:08:35 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-07 10:08:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-07 10:08:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-07 10:08:36 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@70d24586
2025-08-07 10:08:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-07 10:08:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-07 10:08:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-07 10:08:39 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-07 10:08:40 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-07 10:08:40 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-07 10:08:40 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-07 10:08:40 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-07 10:08:42 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-07 10:08:49 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-07 10:08:49 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-07 10:08:49 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-07 10:08:49 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-07 10:08:49 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 23.396 seconds (process running for 24.896)
2025-08-07 10:08:49 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-08-07 10:08:49 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-07 10:08:50 [RMI TCP Connection(11)-172.30.16.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07 10:09:04 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:09:05 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:21:42 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:21:42 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:21:45 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:21:45 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:21:45 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:22:00 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:22:00 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:22:28 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-07 10:22:28 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-07 10:22:30 [SpringApplicationShutdownHook] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-08-07 10:22:30 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-07 10:22:30 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-07 10:22:30 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-07 10:22:30 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-07 10:22:30 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-07 10:22:46 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-07 10:22:46 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 7848 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-07 10:22:46 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-07 10:22:54 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-07 10:22:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-07 10:22:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-07 10:22:55 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6db59e26
2025-08-07 10:22:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-07 10:22:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-07 10:22:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-07 10:22:59 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-07 10:22:59 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-07 10:22:59 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-07 10:23:00 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-07 10:23:00 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-07 10:23:01 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-07 10:23:07 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-07 10:23:07 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-07 10:23:07 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-07 10:23:08 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-07 10:23:08 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 22.669 seconds (process running for 24.136)
2025-08-07 10:23:08 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-08-07 10:23:08 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-07 10:23:23 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07 10:23:23 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:23:23 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:23:26 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:23:26 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:23:27 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:25:38 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:25:38 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:25:40 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:25:41 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:25:41 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:25:47 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-07 10:25:47 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-07 10:25:49 [SpringApplicationShutdownHook] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-08-07 10:25:49 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-07 10:25:49 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-07 10:25:49 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-07 10:25:49 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-07 10:25:49 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-07 10:25:57 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-07 10:25:57 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 13672 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-07 10:25:57 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-07 10:26:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-07 10:26:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-07 10:26:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-07 10:26:06 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@41ad6c00
2025-08-07 10:26:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-07 10:26:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-07 10:26:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-07 10:26:09 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-07 10:26:09 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-07 10:26:09 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-07 10:26:10 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-07 10:26:10 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-07 10:26:11 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-07 10:26:17 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-07 10:26:17 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-07 10:26:17 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-07 10:26:17 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-07 10:26:17 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 22.065 seconds (process running for 23.375)
2025-08-07 10:26:17 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-08-07 10:26:17 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-07 10:26:18 [RMI TCP Connection(7)-172.30.16.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07 10:26:56 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:26:58 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:27:00 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:27:00 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:27:00 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:28:36 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:28:36 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:28:39 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:28:39 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:28:40 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:29:41 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:29:41 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:29:43 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:29:43 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:29:43 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:30:34 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-07 10:30:34 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-07 10:30:36 [SpringApplicationShutdownHook] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-08-07 10:30:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-07 10:30:36 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-07 10:30:36 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-07 10:30:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-07 10:30:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-07 10:30:41 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-07 10:30:41 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 22396 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-07 10:30:41 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-07 10:30:47 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-07 10:30:48 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-07 10:30:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-07 10:30:49 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@32aa27a7
2025-08-07 10:30:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-07 10:30:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-07 10:30:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-07 10:30:52 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-07 10:30:53 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-07 10:30:53 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-07 10:30:54 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-07 10:30:54 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-07 10:30:55 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-07 10:31:02 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-07 10:31:02 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-07 10:31:03 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-07 10:31:03 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-07 10:31:03 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 22.951 seconds (process running for 24.328)
2025-08-07 10:31:03 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-08-07 10:31:03 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-07 10:31:23 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07 10:31:23 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:31:25 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:31:27 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:31:27 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:31:27 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:37:32 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:37:32 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:37:34 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:37:34 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:37:34 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:42:34 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:42:34 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:42:36 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:42:37 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:42:37 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:42:49 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:42:49 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:42:49 [XNIO-1 task-5] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-07 10:42:49 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
